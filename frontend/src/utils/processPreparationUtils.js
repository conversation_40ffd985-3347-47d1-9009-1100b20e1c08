/**
 * 備料工序功能模組
 * 整合數據處理、表單處理和工序選單相關功能
 * （連接線功能已移除）
 */

// ==================== 連接線相關功能已移除 ====================

// CSS 轉義函數，用於轉義 CSS 選擇器中的特殊字符（已停用）
export const escapeCSSSelector = (str) => {
  // 連接線功能已移除
  return str || '';
};

/**
 * 找到相關連接線和工序項目（已停用）
 * @param {string} processId - 工序ID
 * @param {Set} visitedIds - 已訪問的工序ID集合
 * @param {string} direction - 查找方向
 * @param {number} level - 當前遞歸層級
 * @returns {Object} 空對象
 */
export const findAllRelatedItems = (processId, visitedIds = new Set(), direction = 'both', level = 0) => {
  // 連接線功能已移除
  return { lines: [], processes: [] };
};

/**
 * 更新連接線（已停用）
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const updateConnections = (connectionsContainer, savedProcesses) => {
  // 連接線功能已移除
};

/**
 * 處理工序項目滑鼠移入事件（已停用）
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const handleProcessItemMouseEnter = (event, savedProcesses, connectionsContainer) => {
  // 連接線功能已移除
};

/**
 * 處理工序項目滑鼠移出事件（已停用）
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const handleProcessItemMouseLeave = (event, savedProcesses) => {
  // 連接線功能已移除
};

/**
 * 處理材料項目滑鼠移入事件（已停用）
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const handleMaterialItemMouseEnter = (event, savedProcesses, connectionsContainer) => {
  // 連接線功能已移除
};

/**
 * 處理材料項目滑鼠移出事件（已停用）
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const handleMaterialItemMouseLeave = (event, savedProcesses) => {
  // 連接線功能已移除
};

/**
 * 設置工序項目和材料項目的滑鼠事件監聽（已停用）
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const setupProcessItemHoverEvents = (savedProcesses, connectionsContainer) => {
  // 連接線功能已移除
};

/**
 * 高亮固定工序的相關項目（已停用）
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const highlightFixedProcessRelatedItems = (savedProcesses) => {
  // 連接線功能已移除
};



// ==================== 2. 工序數據處理相關功能 ====================

/**
 * 將工序列表保存到 localStorage
 * @param {string} bomId - BOM ID
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const saveProcessesToLocalStorage = (bomId, savedProcesses) => {
  try {
    localStorage.setItem(`processPreparation_${bomId}`, JSON.stringify(savedProcesses));
  } catch (error) {
    console.error('將工序列表保存到 localStorage 時發生錯誤:', error);
  }
};

/**
 * 從 localStorage 加載工序列表
 * @param {string} bomId - BOM ID
 * @returns {Array} 已保存的工序列表
 */
export const loadProcessesFromLocalStorage = (bomId) => {
  try {
    const savedData = localStorage.getItem(`processPreparation_${bomId}`);
    if (savedData) {
      const processes = JSON.parse(savedData);
      // 確保每個工序都有 isCompleted 屬性
      processes.forEach(process => {
        if (process.isCompleted === undefined) {
          process.isCompleted = false;
        }
      });
      return processes;
    }
  } catch (error) {
    console.error('從 localStorage 加載工序列表時發生錯誤:', error);
  }
  return [];
};

/**
 * 前端工序對象轉換為後端工序對象
 * @param {Object} process - 前端工序對象
 * @param {number} order - 順序
 * @returns {Object} 後端工序對象
 */
export const convertProcessToBackendFormat = (process, order) => {
  // 處理 minorProcess
  let minorProcessCode = '';
  if (process.minorProcess) {
    if (typeof process.minorProcess === 'object' && process.minorProcess.code) {
      minorProcessCode = process.minorProcess.code;
    } else if (typeof process.minorProcess === 'string') {
      minorProcessCode = process.minorProcess;
    }
  }
  
  // 處理材料
  let material = null;
  if (process.material) {
    if (typeof process.material === 'object' && process.material.name) {
      material = process.material.name;
    } else if (typeof process.material === 'string') {
      material = process.material;
    }
  }
  
  // 處理來源工序ID和位置
  const sourceProcessId = process.sourceProcessId || null;
  const sourcePosition = process.sourcePosition || process.pieceName || null;
  
  // 處理部位組織和分片組織
  let pieceGroup = '';
  let pieceDetail = '';
  
  if (process.partStructure) {
    if (typeof process.partStructure === 'object' && process.partStructure.code) {
      pieceGroup = process.partStructure.code;
    } else if (typeof process.partStructure === 'string') {
      pieceGroup = process.partStructure;
    }
  }
  
  if (process.pieceStructure) {
    if (typeof process.pieceStructure === 'object' && process.pieceStructure.code) {
      pieceDetail = process.pieceStructure.code;
    } else if (typeof process.pieceStructure === 'string') {
      pieceDetail = process.pieceStructure;
    }
  }
  
  // 根據前端模型轉換為後端需要的格式
  const backendProcess = {
    bomId: process.bomId,
    processCode: process.majorProcess.charAt(0) + minorProcessCode,
    majorProcess: process.majorProcess,
    minorProcess: minorProcessCode,
    pieceGroup: pieceGroup || '未設定',  // 提供默認值避免 NOT NULL 約束錯誤
    pieceDetail: pieceDetail || '未設定', // 提供默認值避免 NOT NULL 約束錯誤
    pieceName: process.pieceName,
    material: material,
    quantity: process.quantity || 1,
    tool: process.tool || null,
    consumable: process.consumable || null,
    processDescription: null,  // 暫時設為 null，可以根據需要修改
    standardTime: 0,  // 暫時設為 0，可以根據需要修改
    actualTime: 0,  // 暫時設為 0，可以根據需要修改
    order: order,
    isCompleted: process.isCompleted === true,  // 保存完成狀態
    isFixed: process.isFixed === true,          // 保存固定狀態
    sourceProcessId: sourceProcessId,           // 保存來源工序ID
    sourcePosition: sourcePosition,             // 保存來源位置名稱
    originalSources: process.originalSources ? JSON.stringify(process.originalSources) : null  // 保存原始來源資料
  };



  // 如果原本有後端ID，則保留
  if (process.backendId) {
    backendProcess.id = process.backendId;
  }

  return backendProcess;
};

/**
 * 後端工序對象轉換為前端工序對象
 * @param {Object} backendProcess - 後端工序對象
 * @param {Object} minorProcessList - 小工序列表資料字典
 * @returns {Object} 前端工序對象
 */
export const convertBackendProcessToFrontend = (backendProcess, minorProcessList) => {
  // 創建前端工序對象
  let minorProcessObj = null;
  
  // 處理 minorProcess
  if (backendProcess.minorProcess && backendProcess.majorProcess && minorProcessList) {
    // 嘗試從 minorProcessList 查找對應的小工序名稱
    const majorProcessKey = backendProcess.majorProcess;
    const minorItems = minorProcessList[majorProcessKey] || [];
    const foundMinor = minorItems.find(item => item.code === backendProcess.minorProcess);
    
    if (foundMinor) {
      minorProcessObj = {
        code: foundMinor.code,
        name: foundMinor.name
      };
    } else {
      // 如果找不到對應的小工序，則創建一個只有代碼的對象
      minorProcessObj = {
        code: backendProcess.minorProcess,
        name: backendProcess.minorProcess // 默認使用代碼作為名稱
      };
    }
  } else {
    // 如果沒有小工序資料，則創建一個空的小工序對象
    minorProcessObj = {
      code: '',
      name: ''
    };
  }
  
  // 確保至少有一個 sourcePosition，默認使用 pieceName
  const sourcePosition = backendProcess.sourcePosition || backendProcess.pieceName;
  

  
  // 處理原始來源資料
  let originalSources = [];
  if (backendProcess.originalSources) {
    try {
      originalSources = JSON.parse(backendProcess.originalSources);
    } catch (error) {
      console.error('解析原始來源資料失敗:', error);
      originalSources = [];
    }
  }

  return {
    id: backendProcess.id, // 直接使用後端ID作為前端ID，確保穩定性
    backendId: backendProcess.id, // 保存後端ID
    bomId: backendProcess.bomId,
    pieceName: backendProcess.pieceName,
    majorProcess: backendProcess.majorProcess,
    minorProcess: minorProcessObj,
    sequenceNumber: backendProcess.sequenceNumber || '',
    sourcePosition: sourcePosition,
    sourceProcessId: backendProcess.sourceProcessId || null,
    tool: backendProcess.tool,
    consumable: backendProcess.consumable,
    quantity: backendProcess.quantity || 1,
    partStructure: backendProcess.pieceGroup,
    pieceStructure: backendProcess.pieceDetail,
    material: backendProcess.material,
    isCompleted: backendProcess.isCompleted === true, // 確保是布爾值
    isFixed: backendProcess.isFixed === true,      // 確保是布爾值
    originalSources: originalSources  // 恢復原始來源資料
  };
};

/**
 * 從後端加載工序列表
 * @param {string} bomId - BOM ID
 * @param {object} api - API 對象
 * @param {object} minorProcessList - 小工序列表資料字典
 * @returns {Promise<Array>} 已保存的工序列表
 */
export const loadProcessesFromBackend = async (bomId, api, minorProcessList) => {
  try {
    const response = await api.processPreparation.getByBomId(bomId);
    if (response.status === 'success' && Array.isArray(response.data)) {
      // 將後端工序轉換為前端工序格式
      const frontendProcesses = response.data.map(backendProcess => {

        
        return convertBackendProcessToFrontend(backendProcess, minorProcessList);
      });
      
      return frontendProcesses;
    }
  } catch (error) {
    console.error('從後端加載工序列表時發生錯誤:', error);
  }
  return [];
};

/**
 * 同步工序列表到後端
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} bomId - BOM ID
 * @param {object} api - API 對象
 * @returns {Promise<boolean>} 是否成功同步
 */
export const syncProcessesToBackend = async (savedProcesses, bomId, api) => {
  try {
    // 獲取後端現有工序
    const existingProcessesResponse = await api.processPreparation.getByBomId(bomId);
    const existingProcesses = existingProcessesResponse.status === 'success' ? existingProcessesResponse.data : [];
    const existingProcessIds = new Set(existingProcesses.map(p => p.id));

    // 標記要刪除的工序
    const frontendProcessBackendIds = new Set(savedProcesses.filter(p => p.backendId).map(p => p.backendId));
    const toDeleteIds = [...existingProcessIds].filter(id => !frontendProcessBackendIds.has(id));

    // 刪除不再存在的工序
    for (const id of toDeleteIds) {
      await api.processPreparation.delete(id);
    }

    // 更新或創建工序
    for (let i = 0; i < savedProcesses.length; i++) {
      const process = savedProcesses[i];
      const order = i + 1; // 使用陣列索引作為順序，從1開始
      const backendProcess = convertProcessToBackendFormat(process, order);
      
      // 如果已經有後端ID，則更新，否則創建新工序
      if (process.backendId) {
        const updateResponse = await api.processPreparation.update(process.backendId, backendProcess);
        if (updateResponse.status !== 'success') {
          console.error(`更新工序失敗: ${process.backendId}`);
        }
      } else {
        // 確保有bomId
        backendProcess.bomId = bomId;
        const createResponse = await api.processPreparation.create(backendProcess);
        if (createResponse.status === 'success' && createResponse.data) {
          // 保存後端ID到前端工序對象
          savedProcesses[i].backendId = createResponse.data.id;
          // 如果是臨時ID，則更新為後端ID以確保排序穩定性
          if (savedProcesses[i].id.startsWith('temp_')) {
            savedProcesses[i].id = createResponse.data.id;
          }
        } else {
          console.error('創建工序失敗:', backendProcess);
        }
      }
    }

    // 更新本地存儲
    saveProcessesToLocalStorage(bomId, savedProcesses);
    return true;
  } catch (error) {
    console.error('同步工序列表到後端時發生錯誤:', error);
    return false;
  }
};

/**
 * 更新工序的完成狀態
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} processId - 工序ID
 * @param {boolean} isCompleted - 是否完成
 * @returns {boolean} 是否成功更新
 */
export const updateProcessCompletionStatus = (savedProcesses, processId, isCompleted) => {
  if (!processId) return false;

  // 找到對應的工序並標記狀態
  const processIndex = savedProcesses.findIndex(p => p.id === processId);
  if (processIndex !== -1) {
    // 更新工序的完成狀態
    savedProcesses[processIndex].isCompleted = isCompleted;
    return true;
  }

  return false;
};

/**
 * 標記工序階段完成
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} processId - 工序ID
 * @param {string} bomId - BOM ID
 * @returns {boolean} 是否成功標記
 */
export const markStageComplete = (savedProcesses, processId, bomId) => {
  const result = updateProcessCompletionStatus(savedProcesses, processId, true);
  if (result) {
    saveProcessesToLocalStorage(bomId, savedProcesses);
  }
  return result;
};

/**
 * 取消標記工序階段完成
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} processId - 工序ID
 * @param {string} bomId - BOM ID
 * @returns {boolean} 是否成功取消標記
 */
export const markStageIncomplete = (savedProcesses, processId, bomId) => {
  const result = updateProcessCompletionStatus(savedProcesses, processId, false);
  if (result) {
    saveProcessesToLocalStorage(bomId, savedProcesses);
  }
  return result;
};

/**
 * 清除所有工序
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} bomId - BOM ID
 * @param {Object} processCounters - 工序計數器
 * @param {Function} updateConnectionsCallback - 更新連接線的回調函數（已停用）
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素（已停用）
 * @param {object} api - API 對象 (可選)
 */
export const clearAllProcesses = async (savedProcesses, bomId, processCounters, updateConnectionsCallback, connectionsContainer, api = null) => {
  // 如果提供了API，先刪除後端資料
  if (api) {
    try {
      // 獲取所有現有工序
      const response = await api.processPreparation.getByBomId(bomId);
      if (response.status === 'success' && Array.isArray(response.data)) {
        // 刪除每一個工序
        for (const process of response.data) {
          await api.processPreparation.delete(process.id);
        }
      }
    } catch (error) {
      console.error('刪除後端工序失敗:', error);
    }
  }

  // 清空工序列表
  savedProcesses.splice(0, savedProcesses.length);

  // 保存到 localStorage
  saveProcessesToLocalStorage(bomId, savedProcesses);

  // 重置計數器
  Object.keys(processCounters).forEach(key => {
    processCounters[key] = 1;
  });

  // 連接線功能已移除
};

/**
 * 刪除指定工序
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} processId - 工序ID
 * @param {string} bomId - BOM ID
 * @param {Function} updateConnectionsCallback - 更新連接線的回調函數（已停用）
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素（已停用）
 * @param {object} api - API 對象 (可選)
 * @returns {boolean} 是否成功刪除
 */
export const deleteProcess = async (savedProcesses, processId, bomId, updateConnectionsCallback, connectionsContainer, api = null) => {
  if (!processId) return false;

  // 找到要刪除的工序索引
  const processIndex = savedProcesses.findIndex(p => p.id === processId);

  if (processIndex !== -1) {
    const process = savedProcesses[processIndex];
    
    // 如果提供了API且工序有後端ID，則從後端刪除
    if (api && process.backendId) {
      try {
        await api.processPreparation.delete(process.backendId);
      } catch (error) {
        console.error(`刪除後端工序失敗: ${process.backendId}`, error);
        // 即使後端刪除失敗，也要從前端刪除
      }
    }

    // 從工序列表中刪除該工序
    savedProcesses.splice(processIndex, 1);

    // 保存到 localStorage
    saveProcessesToLocalStorage(bomId, savedProcesses);

    // 如果提供了API，則同步剩餘工序順序
    // 注意：只同步有backendId的工序，避免重新創建已刪除的工序
    if (api) {
      try {
        // 獲取後端現有工序
        const existingProcessesResponse = await api.processPreparation.getByBomId(bomId);
        const existingProcesses = existingProcessesResponse.status === 'success' ? existingProcessesResponse.data : [];
        const existingProcessIds = new Set(existingProcesses.map(p => p.id));

        // 標記要刪除的工序
        const frontendProcessBackendIds = new Set(savedProcesses.filter(p => p.backendId).map(p => p.backendId));
        const toDeleteIds = [...existingProcessIds].filter(id => !frontendProcessBackendIds.has(id));

        // 刪除不再存在的工序
        for (const id of toDeleteIds) {
          await api.processPreparation.delete(id);
        }

        // 同步剩餘工序
        const processesToSync = savedProcesses.filter(p => !p.backendId);
        for (const process of processesToSync) {
          try {
            const backendProcess = convertProcessToBackendFormat(process, savedProcesses.indexOf(process) + 1);
            const response = await api.processPreparation.create(backendProcess);
            if (response.status === 'success' && response.data) {
              process.backendId = response.data.id;
            }
          } catch (error) {
            console.error(`同步工序到後端失敗: ${process.id}`, error);
          }
        }

        // 更新工序順序
        for (let i = 0; i < savedProcesses.length; i++) {
          const process = savedProcesses[i];
          if (process.backendId) {
            try {
              await api.processPreparation.update(process.backendId, { order: i +1 });
            } catch (error) {
              console.error(`更新工序順序失敗: ${process.backendId}`, error);
            }
          }
        }
      } catch (error) {
        console.error('同步工序到後端失敗:', error);
      }
    }

    // 連接線功能已移除

    return true;
  }

  return false;
};



/**
 * 保存工序
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {Object} processForm - 工序表單數據
 * @param {string} selectedProcessId - 選擇的工序ID
 * @param {string} selectedPosition - 選擇的部位
 * @param {string} generatedProcessCode - 生成的工序代碼
 * @param {Object} processCounters - 工序計數器
 * @param {string} bomId - BOM ID
 * @param {object} api - API 對象 (可選)
 * @returns {boolean} 是否成功保存
 */
export const saveProcess = (savedProcesses, processForm, selectedProcessId, selectedPosition, generatedProcessCode, processCounters, bomId, api = null) => {
  // 驗證必填欄位
  if (!processForm.pieceName) {
    return false;
  }

  // 判斷是更新還是新增
  const isUpdate = !!selectedProcessId;

  if (isUpdate) {
    // 更新現有工序
    const processIndex = savedProcesses.findIndex(p => p.id === selectedProcessId);
    if (processIndex !== -1) {
      // 保留原來的 ID 和來源信息
      const originalProcess = savedProcesses[processIndex];
      const originalId = originalProcess.id;
      const originalBackendId = originalProcess.backendId;
      const originalSourcePosition = originalProcess.sourcePosition;
      const originalSourceProcessId = originalProcess.sourceProcessId;
      const originalIsCompleted = originalProcess.isCompleted;
      const originalIsFixed = originalProcess.isFixed;
      const originalOriginalSources = originalProcess.originalSources;

      // 更新工序數據
      savedProcesses[processIndex] = {
        ...originalProcess,
        pieceName: processForm.pieceName,
        majorProcess: processForm.majorProcess,
        minorProcess: processForm.minorProcess ? {
          code: processForm.minorProcess.code,
          name: processForm.minorProcess.name
        } : null,
        sequenceNumber: originalProcess.sequenceNumber,
        tool: processForm.tool,
        consumable: processForm.consumable,
        quantity: processForm.quantity,
        partStructure: processForm.partStructure ? processForm.partStructure.code : null,
        pieceStructure: processForm.pieceStructure ? processForm.pieceStructure.code : null,
        material: processForm.material || null,
        // 保留原有ID和狀態
        id: originalId,
        backendId: originalBackendId,
        sourcePosition: originalSourcePosition,
        sourceProcessId: originalSourceProcessId,
        isCompleted: originalIsCompleted,
        isFixed: originalIsFixed,
        originalSources: originalOriginalSources
      };

      // 保存到 localStorage
      saveProcessesToLocalStorage(bomId, savedProcesses);

      // 如果提供了API，則同步到後端
      if (api) {
        syncProcessesToBackend(savedProcesses, bomId, api).catch(error => {
          console.error('同步工序到後端失敗:', error);
        });
      }

      return true;
    }
  } else {
    // 創建新的工序項目
    const newProcess = {
      id: `temp_${Date.now()}_${Math.floor(Math.random() * 1000)}`, // 使用更穩定的臨時ID格式
      pieceName: processForm.pieceName,
      majorProcess: processForm.majorProcess,
      minorProcess: processForm.minorProcess ? {
        code: processForm.minorProcess.code,
        name: processForm.minorProcess.name
      } : null,
      sequenceNumber: '',
      sourcePosition: selectedPosition,
      tool: processForm.tool,
      consumable: processForm.consumable,
      quantity: processForm.quantity,
      // 不再使用 currentProcessRestriction，直接記錄來源工序的ID
      sourceProcess: null,
      // 如果有選擇工序，則記錄來源工序的ID
      sourceProcessId: processForm.sourceProcessId,
      // 添加完成狀態標記
      isCompleted: false,
      // 保存部位組織、分片組織和材料等信息
      partStructure: processForm.partStructure ? processForm.partStructure.code : null,
      pieceStructure: processForm.pieceStructure ? processForm.pieceStructure.code : null,
      material: processForm.material || null,
      // 添加bomId
      bomId: bomId
    };

    // 將新工序添加到已保存的工序列表中
    savedProcesses.push(newProcess);

    // 保存到 localStorage
    saveProcessesToLocalStorage(bomId, savedProcesses);

    // 如果提供了API，則同步到後端
    if (api) {
      syncProcessesToBackend(savedProcesses, bomId, api).catch(error => {
        console.error('同步工序到後端失敗:', error);
      });
    }

    // 增加計數器
    if (processForm.majorProcess) {
      // 大工序代號已經包含在 majorProcess 的第一個字符中
      const majorProcessKey = processForm.majorProcess.charAt(0);
      if (processCounters[majorProcessKey]) {
        processCounters[majorProcessKey]++;
      }
    }

    return true;
  }

  return false;
};

// ==================== 3. 表單處理相關功能 ====================

/**
 * 填充分片組織資訊到表單
 * @param {Object} process - 工序對象
 * @param {Object} targetForm - 目標表單對象
 * @param {Array} partStructureList - 部位組織列表
 * @param {Object} pieceStructureList - 分片組織列表
 */
export const fillPieceStructureInfo = (process, targetForm, partStructureList, pieceStructureList) => {
  if (!process || !targetForm) return;

  // 填入部位組織
  if (process.partStructure) {
    const partStructure = partStructureList.find(p => p.code === process.partStructure);
    if (partStructure) {
      targetForm.partStructure = partStructure;
    }
  }

  // 填入分片組織
  if (process.pieceStructure) {
    // 找到對應的分片組織
    let pieceStructure = null;

    // 遍歷所有部位的分片組織
    Object.values(pieceStructureList).forEach(pieces => {
      const found = pieces.find(p => p.code === process.pieceStructure);
      if (found) {
        pieceStructure = found;
      }
    });

    if (pieceStructure) {
      targetForm.pieceStructure = pieceStructure;
    }
  }

  // 填入材料
  if (process.material) {
    targetForm.material = process.material;
  }
};

/**
 * 選擇下一個工序
 * @param {Object} process - 工序對象
 * @param {Object} processForm - 工序表單數據
 * @param {Array} partStructureList - 部位組織列表
 * @param {Object} pieceStructureList - 分片組織列表
 * @returns {Object} 包含選擇的部位和工序ID的對象
 */
export const selectNextProcess = (process, processForm, partStructureList, pieceStructureList) => {
  if (!process) return { selectedPosition: '', selectedProcessId: null };

  // 將原有工序的所有數據填入表單
  processForm.pieceName = process.pieceName || '';
  processForm.majorProcess = process.majorProcess || '';
  processForm.minorProcess = process.minorProcess || '';
  processForm.tool = process.tool || '';
  processForm.consumable = process.consumable || '';
  processForm.quantity = process.quantity || 1;
  processForm.partStructure = '';
  processForm.pieceStructure = '';

  // 使用通用函數填充分片組織資訊
  fillPieceStructureInfo(process, processForm, partStructureList, pieceStructureList);

  return {
    selectedPosition: process.pieceName,
    selectedProcessId: process.id
  };
};

/**
 * 處理大工序變更
 * @param {Object} processForm - 工序表單數據
 */
export const handleMajorProcessChange = (processForm) => {
  // 重置小工序、工具和耗材
  processForm.minorProcess = '';
  processForm.tool = '';
  processForm.consumable = '';
};

/**
 * 處理小工序變更
 * @param {Object} processForm - 工序表單數據
 */
export const handleMinorProcessChange = (processForm) => {
  // 重置工具和耗材
  processForm.tool = '';
  processForm.consumable = '';
};

/**
 * 處理部位組織變更
 * @param {Object} processForm - 工序表單數據
 */
export const handlePartStructureChange = (processForm) => {
  // 重置分片組織
  processForm.pieceStructure = '';
};



// ==================== 4. 工序選單相關功能 ====================

/**
 * 顯示工序選單
 * @param {Object} process - 工序對象
 * @param {Event} event - 點擊事件
 * @returns {Object} 包含選單樣式和當前工序的對象
 */
export const showProcessMenu = (process, event) => {
  if (!process) return { processMenuStyle: {}, currentProcess: null };

  // 阻止事件冒泡
  event.stopPropagation();

  // 計算選單位置
  const rect = event.target.getBoundingClientRect();
  const processMenuStyle = {
    top: `${rect.top}px`,
    left: `${rect.right + 5}px`
  };

  return {
    processMenuStyle,
    currentProcess: process
  };
};

/**
 * 添加下一階段工序
 * @param {Object} currentProcess - 當前工序對象
 * @param {Object} processForm - 工序表單數據
 * @param {Array} partStructureList - 部位組織列表
 * @param {Object} pieceStructureList - 分片組織列表
 * @returns {Object} 包含選擇的部位和工序表單的對象
 */
export const addNextProcess = (currentProcess, processForm, partStructureList, pieceStructureList) => {
  if (!currentProcess) return { selectedPosition: '', processForm: {} };

  // 重置表單數據
  processForm.pieceName = currentProcess.pieceName; // 預設分片名稱為原本的分片名稱
  processForm.partStructure = '';
  processForm.pieceStructure = '';
  processForm.material = '';
  processForm.majorProcess = '';
  processForm.minorProcess = '';
  processForm.tool = '';
  processForm.consumable = '';
  processForm.quantity = 1;
  processForm.sourceProcessId = currentProcess.id; // 設置來源工序 ID

  // 使用通用函數填充分片組織資訊
  fillPieceStructureInfo(currentProcess, processForm, partStructureList, pieceStructureList);

  return {
    selectedPosition: currentProcess.pieceName,
    processForm
  };
};

/**
 * 切換工序固定狀態
 * @param {Object} currentProcess - 當前工序對象
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {string} bomId - BOM ID
 * @param {Function} updateConnectionsCallback - 更新連接線的回調函數（已停用）
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素（已停用）
 * @returns {boolean} 是否成功切換
 */
export const toggleFixProcess = (currentProcess, savedProcesses, bomId, updateConnectionsCallback, connectionsContainer) => {
  if (!currentProcess) return false;

  // 切換固定狀態
  const processIndex = savedProcesses.findIndex(p => p.id === currentProcess.id);
  if (processIndex !== -1) {
    // 更新固定狀態
    const isCurrentlyFixed = !!savedProcesses[processIndex].isFixed;

    // 如果是固定狀態，找到整條工序線路並設置為固定
    if (!isCurrentlyFixed) {
      // 設置當前工序為固定
      savedProcesses[processIndex].isFixed = true;

      // 更新當前工序的DOM元素的fixed類別
      const currentProcessElement = document.querySelector(`.process-item[data-id="${currentProcess.id}"]`);
      if (currentProcessElement) {
        currentProcessElement.classList.add('fixed');
      }

      // 找到整條工序線路（包括上游和下游的所有工序）
      // 使用 'both' 模式，遞歸查找所有相關工序
      const { processes: relatedProcesses } = findAllRelatedItems(currentProcess.id, new Set(), 'both');

      // 獲取所有相關工序的 ID
      const relatedProcessIds = relatedProcesses
        .map(item => item.getAttribute('data-id'))
        .filter(id => id && id !== currentProcess.id); // 排除當前工序

      // 更新所有相關工序的固定狀態
      relatedProcessIds.forEach(id => {
        const index = savedProcesses.findIndex(p => p.id === id);
        if (index !== -1) {
          savedProcesses[index].isFixed = true;

          // 為相關工序的DOM元素添加fixed類別
          const processElement = document.querySelector(`.process-item[data-id="${id}"]`);
          if (processElement) {
            processElement.classList.add('fixed');
          }
        }
      });
    } else {
      // 取消固定時，找到與當前工序相關的所有固定工序，並取消它們的固定狀態

      // 首先，找到整條工序線路（包括上游和下游的所有工序）
      const { processes: relatedProcesses } = findAllRelatedItems(currentProcess.id, new Set(), 'both');

      // 獲取所有相關工序的 ID，包括當前工序
      const relatedProcessIds = relatedProcesses
        .map(item => item.getAttribute('data-id'))
        .filter(id => id); // 包括當前工序

      // 取消當前工序的固定狀態
      savedProcesses[processIndex].isFixed = false;

      // 更新當前工序的DOM元素的fixed類別
      const currentProcessElement = document.querySelector(`.process-item[data-id="${currentProcess.id}"]`);
      if (currentProcessElement) {
        currentProcessElement.classList.remove('fixed');
      }

      // 取消所有相關工序的固定狀態
      relatedProcessIds.forEach(id => {
        const index = savedProcesses.findIndex(p => p.id === id);
        if (index !== -1) {
          savedProcesses[index].isFixed = false;

          // 移除相關工序的DOM元素的fixed類別
          const processElement = document.querySelector(`.process-item[data-id="${id}"]`);
          if (processElement) {
            processElement.classList.remove('fixed');
          }
        }
      });
    }

    // 保存到 localStorage
    saveProcessesToLocalStorage(bomId, savedProcesses);

    // 連接線功能已移除

    return true;
  }

  return false;
};